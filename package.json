{"name": "trust-computing-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/x": "^1.2.0", "@aws-sdk/client-s3": "^3.797.0", "@gradio/client": "^1.13.1", "@radix-ui/react-slot": "^1.1.0", "antd": "^5.24.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "markdown-it": "^14.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.13.0", "@types/markdown-it": "^14.1.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.4.49", "prettier": "^3.5.3", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}