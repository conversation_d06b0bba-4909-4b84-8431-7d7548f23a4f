import { Form, Input, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import useUserStore from '@/store/user';
import { login, getUserInfo } from './login.service';

const Login = () => {
  const [form] = Form.useForm();
  const setUser = useUserStore((state) => state.setUser);
  const navigate = useNavigate();
  const onFinish = async (values: any) => {
    // const res = await login(values);
    // console.log(res);
    // const userInfo = await getUserInfo();
    // console.log(userInfo);
    const userInfo = {
      username: values.username,
      token: '1234567890',
      role: 1,
    };
    if (values.username === 'admin') {
      userInfo.role = 1;
    } else {
      userInfo.role = 2;
    }
    // userInfo.role = 1;
    setUser(userInfo);
    navigate(userInfo.role === 1 ? '/playground' : '/chat');
  };
  return (
    <div className="bg-gray-100 h-screen flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md w-128">
        <h1 className="text-2xl font-bold mb-6">欢迎登录大模型隐私保护计算平台</h1>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" className="w-full" htmlType="submit">
              登录
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default Login;
