# 数据管理页面

这是信任计算平台的数据管理页面，用于管理数据源和授权。

## 功能特性

### 1. 数据源管理
- **数据源列表**: 显示所有已配置的数据源
- **搜索功能**: 支持按数据源名称搜索
- **类型筛选**: 支持按数据源类型筛选（LOCAL、REMOTE等）
- **添加数据源**: 通过弹框添加新的数据源

### 2. 表格字段
- **数据源名称**: 数据源的唯一标识
- **数据连接类型**: 数据连接的详细描述
- **已连接表名**: 当前连接的数据表
- **根密码名称**: 数据库根用户密码标识
- **数据密码名称**: 数据集访问密码标识
- **所属用户**: 数据源的所有者
- **状态**: 数据源当前状态（运行中/已停止）

### 3. 操作功能
- **刷新**: 刷新数据源状态
- **授权管理**: 管理数据源的用户授权
- **删除**: 删除数据源

### 4. 弹框功能

#### 添加数据源弹框
- 数据源名称配置
- 数据源类型选择（LOCAL、REMOTE、MySQL、PostgreSQL、Oracle）
- 连接字符串配置
- 用户名和密码设置
- 描述信息

#### 授权管理弹框
- 用户授权列表
- 添加新授权
- 权限管理（读取、写入、查询、删除）
- 角色分配（数据提供方、数据使用方、管理员）

## 测试数据

页面包含了完整的测试数据：
- 1条数据源记录（LOCAL类型）
- 完整的表格字段展示
- 状态指示器显示

## 样式特性

- 使用CSS Modules进行样式管理
- 响应式设计
- 状态指示器（绿色圆点表示运行中）
- 操作按钮悬停效果
- 弹框表单验证

## 路由配置

页面已在路由中注册为 `/datasets`，可通过侧边栏"数据管理"菜单访问。

## 组件结构

```
src/views/datasets/
├── index.tsx                    # 主页面组件
├── index.module.css            # 样式文件
├── README.md                   # 说明文档
└── components/
    ├── add-datasource-modal.tsx # 添加数据源弹框
    └── auth-manage-modal.tsx    # 授权管理弹框
```

## 使用说明

1. **查看数据源**: 页面加载后自动显示数据源列表
2. **搜索数据源**: 在搜索框中输入名称进行搜索
3. **筛选数据源**: 使用下拉框按类型筛选
4. **添加数据源**: 点击"添加数据源"按钮打开配置弹框
5. **管理授权**: 点击表格中的"授权管理"按钮打开授权弹框
6. **刷新状态**: 点击"刷新"按钮更新数据源状态

## 后续扩展

可以根据实际需求扩展以下功能：
- 数据源连接测试
- 批量操作
- 数据源监控
- 权限审计日志
- 数据源备份和恢复
