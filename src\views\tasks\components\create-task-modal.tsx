import React from 'react';
import { Modal, Form, Input, Select, Button, Space, message } from 'antd';

interface CreateTaskModalProps {
  open: boolean;
  onClose: () => void;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({ open, onClose }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('创建任务:', values);
      
      // 这里处理任务创建逻辑
      message.success('任务创建成功');
      form.resetFields();
      onClose();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title="新建任务"
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          taskType: 'training',
          priority: 'normal',
        }}
      >
        <Form.Item
          name="taskName"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入任务名称" />
        </Form.Item>

        <Form.Item
          name="taskType"
          label="任务类型"
          rules={[{ required: true, message: '请选择任务类型' }]}
        >
          <Select
            options={[
              { value: 'training', label: '训练任务' },
              { value: 'inference', label: '推理任务' },
              { value: 'evaluation', label: '评估任务' },
              { value: 'data_processing', label: '数据处理' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="participants"
          label="参与方"
          rules={[{ required: true, message: '请选择参与方' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择参与方"
            options={[
              { value: 'alice', label: 'Alice' },
              { value: 'bob', label: 'Bob' },
              { value: 'charlie', label: 'Charlie' },
              { value: 'david', label: 'David' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="dataset"
          label="数据集"
          rules={[{ required: true, message: '请选择数据集' }]}
        >
          <Select
            placeholder="请选择数据集"
            options={[
              { value: 'dataset1', label: '数据集1' },
              { value: 'dataset2', label: '数据集2' },
              { value: 'dataset3', label: '数据集3' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="algorithm"
          label="算法"
          rules={[{ required: true, message: '请选择算法' }]}
        >
          <Select
            placeholder="请选择算法"
            options={[
              { value: 'fedavg', label: 'FedAvg' },
              { value: 'fedprox', label: 'FedProx' },
              { value: 'scaffold', label: 'SCAFFOLD' },
              { value: 'fednova', label: 'FedNova' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="priority"
          label="优先级"
        >
          <Select
            options={[
              { value: 'high', label: '高' },
              { value: 'normal', label: '普通' },
              { value: 'low', label: '低' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="rounds"
          label="训练轮数"
          rules={[{ required: true, message: '请输入训练轮数' }]}
        >
          <Input type="number" placeholder="请输入训练轮数" min={1} />
        </Form.Item>

        <Form.Item
          name="learningRate"
          label="学习率"
        >
          <Input type="number" placeholder="请输入学习率" step={0.001} />
        </Form.Item>

        <Form.Item
          name="batchSize"
          label="批次大小"
        >
          <Input type="number" placeholder="请输入批次大小" min={1} />
        </Form.Item>

        <Form.Item
          name="description"
          label="任务描述"
        >
          <Input.TextArea 
            placeholder="请输入任务描述（可选）"
            rows={3}
          />
        </Form.Item>

        <Form.Item>
          <Space className="w-full justify-end">
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit}>
              创建任务
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateTaskModal;
