import { createBrowserRouter } from 'react-router-dom';
import Login from '@/views/login';
import LayoutMain from '@/layout/main';
import Home from '@/views/home';
import Page404 from '@/views/404';

import LogPage from '@/views/log';
import NoAuth from '@/views/no-auth';
import DatasetList from '@/views/datasets';
import TaskCenter from '@/views/tasks';

const router = createBrowserRouter([
  {
    path: '/',
    element: <LayoutMain />,
    errorElement: <Page404 />, // Error boundary for the route
    children: [
      {
        path: 'home',
        element: <Home />,
      },
      {
        path: 'datasets',
        element: <DatasetList />,
      },
      {
        path: 'tasks',
        element: <TaskCenter />,
      },
      {
        path: 'logs',
        element: <LogPage />,
      },
    ],
  },
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/404',
    element: <Page404 />,
  },
  {
    path: '/no-auth',
    element: <NoAuth />,
  },
]);
export default router;
