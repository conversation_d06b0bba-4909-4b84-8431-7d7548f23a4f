import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { <PERSON>ton, Card, Typography, Descriptions, Table } from 'antd';
import { LeftOutlined, EditOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface DatasetPreviewItem {
  key: string;
  index: number;
  instruction: string;
  input: string;
  output: string;
}

const DatasetDetail: React.FC = () => {
  const { id: datasetId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  console.log(datasetId);

  /* Mock data based on the image.
     In a real app, you would fetch this from an API using the datasetId parameter */
  const [datasetDetail] = useState({
    name: 'alpaca_en_demo',
    type: '训练集',
    format: 'prompt+response',
    permission: '推理; 训练; 优化; 测评',
    totalTokens: '215.23K',
    description: '英文数据集',
    createTime: '2025-02-20 13:53:26',
  });

  // Mock preview data
  const [previewData] = useState<DatasetPreviewItem[]>([
    {
      key: '1',
      index: 1,
      instruction: 'Describe a process of making crepes.',
      input: '',
      output: 'Making crepes is an easy and delicious process! H...',
    },
    {
      key: '2',
      index: 2,
      instruction: 'Transform the following sentence using a synony...',
      input: '',
      output: 'The car accelerated rapidly.',
    },
    {
      key: '3',
      index: 3,
      instruction: 'Make a persuasive argument to promote recycling.',
      input: '',
      output: 'Recycling is an incredibly important action that we...',
    },
    {
      key: '4',
      index: 4,
      instruction: 'Invent a new word by combining two existing words.',
      input: '',
      output: '"Chillax" - a combination of the words "chill" and ...',
    },
    {
      key: '5',
      index: 5,
      instruction: 'Give an example of a job that a computer can do b...',
      input: '',
      output: 'An example of a job that a computer can do better...',
    },
    {
      key: '6',
      index: 6,
      instruction: 'Given the parameters of a triangle, find out its peri...',
      input: 'Side 1 = 4 Side 2 = 6 Side 3 = 8',
      output: 'The perimeter of a triangle is the sum of the lengt...',
    },
  ]);

  const previewColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
    },
    {
      title: 'instruction',
      dataIndex: 'instruction',
      key: 'instruction',
      width: 300,
    },
    {
      title: 'input',
      dataIndex: 'input',
      key: 'input',
      width: 200,
    },
    {
      title: 'output',
      dataIndex: 'output',
      key: 'output',
      width: 300,
    },
  ];

  const handleGoBack = () => {
    navigate(-1);
  };

  

  return (
    <div className="bg-gray-50 p-4">
      {/* Top navigation */}
      <div className="flex items-center mb-4 justify-between">
        <div className="flex items-center cursor-pointer" onClick={handleGoBack}>
          <LeftOutlined />
          <Typography.Text className="ml-2">{datasetDetail.name}</Typography.Text>
        </div>
        <Button>删除</Button>
      </div>

      {/* Basic information section */}
      <Card className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <Title level={5} className="m-0">
            基础信息
          </Title>
          <Button type="text" icon={<EditOutlined />} />
        </div>

        <Descriptions column={3}>
          <Descriptions.Item label="数据集类型">{datasetDetail.type}</Descriptions.Item>
          <Descriptions.Item label="格式">{datasetDetail.format}</Descriptions.Item>
          <Descriptions.Item label="@tokens">
            {datasetDetail.totalTokens}
          </Descriptions.Item>
          <Descriptions.Item label="描述">
            {datasetDetail.description}
          </Descriptions.Item>
          <Descriptions.Item label="权限">{datasetDetail.permission}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {datasetDetail.createTime}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* Dataset preview section */}
      <Card className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <Title level={5} className="m-0">
            数据集预览
          </Title>
        </div>
        <Table
          columns={previewColumns}
          dataSource={previewData}
          pagination={{
            total: previewData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </div>
  );
};

export default DatasetDetail;
