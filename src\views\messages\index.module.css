.messageContainer {
  padding: 24px;
  background-color: #fff;
  min-height: 100vh;
}

.messageTabs {
  margin-bottom: 0;
}

.messageTabs .ant-tabs-nav {
  margin-bottom: 16px;
}

.messageTabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.messageTabs .ant-tabs-tab-active {
  color: #1890ff;
}

.searchBar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.filterSelect {
  width: 120px;
}

.searchInput {
  width: 240px;
  margin-left: auto;
}

.messageTable {
  background: #fff;
  border-radius: 6px;
}

.messageTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.messageTable .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.messageTable .ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
}

.messageTable .ant-btn-link:hover {
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .searchBar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .filterSelect,
  .searchInput {
    width: 100%;
  }
  
  .searchInput {
    margin-left: 0;
  }
  
  .messageTable {
    overflow-x: auto;
  }
}
