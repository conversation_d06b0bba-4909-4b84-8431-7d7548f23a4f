import React from 'react';
import { Drawer, Button, Form, Upload, Input, Select } from 'antd';
import { InboxOutlined } from '@ant-design/icons';

const { Dragger } = Upload;

interface UploadFile {
  uid: string;
  name: string;
  status: 'done' | 'uploading' | 'error';
  response?: any;
}

interface DatasetFormValues {
  datasetFile: UploadFile;
  name: string;
  type: string;
  format: string;
  permissions: string[];
  description: string;
}

interface AddDatasetDrawerProps {
  open: boolean;
  onClose: () => void;
  onFinish: (values: DatasetFormValues) => void;
}

const AddDatasetDrawer: React.FC<AddDatasetDrawerProps> = ({
  open,
  onClose,
  onFinish,
}) => {
  const [form] = Form.useForm();

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const handleFinish = (values: DatasetFormValues) => {
    onFinish(values);
    form.resetFields();
  };

  return (
    <Drawer
      title="添加数据集"
      width={500}
      open={open}
      onClose={handleClose}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={handleClose} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button type="primary" onClick={() => form.submit()}>
            确定
          </Button>
        </div>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleFinish}>
        <Form.Item
          name="name"
          label="数据集名称"
          rules={[{ required: true, message: '请输入数据集名称' }]}
        >
          <Input placeholder="请输入数据集名称" />
        </Form.Item>

        <Form.Item
          name="type"
          label="数据集类型"
          rules={[{ required: true, message: '请选择数据集类型' }]}
        >
          <Select placeholder="请选择数据集类型">
            <Select.Option value="training">训练集</Select.Option>
            <Select.Option value="testing">测试集</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="format"
          label="数据集格式"
          rules={[{ required: true, message: '请选择数据集格式' }]}
        >
          <Select placeholder="请选择数据集格式">
            <Select.Option value="prompt_response">Prompt+Response</Select.Option>
            <Select.Option value="text">纯文本</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="permissions"
          label="数据集授权"
          rules={[{ required: true, message: '请选择数据集授权' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择数据集授权"
            options={[
              { label: '推理', value: 'inference' },
              { label: '训练', value: 'training' },
              { label: '测评', value: 'evaluation' },
              { label: '优化', value: 'optimization' },
            ]}
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="数据集描述"
          rules={[{ required: true, message: '请输入数据集描述' }]}
        >
          <Input.TextArea rows={4} placeholder="请输入数据集描述" />
        </Form.Item>

        <Form.Item
          name="datasetFile"
          label="数据集文件"
          rules={[{ required: true, message: '请上传数据集文件' }]}
        >
          <Dragger
            name="file"
            multiple={false}
            action="/api/upload"
            onChange={(info) => {
              if (info.file.status === 'done') {
                console.log('File uploaded successfully');
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">支持CSV、TXT格式</p>
          </Dragger>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default AddDatasetDrawer;
