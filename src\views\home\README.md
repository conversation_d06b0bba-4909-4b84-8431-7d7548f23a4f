# Home 页面

这是信任计算平台的首页，包含了系统的核心监控数据和任务管理功能。

## 功能特性

### 1. 统计卡片区域
- **总任务数**: 显示系统中的总任务数量，包含增长趋势
- **活跃节点**: 显示当前活跃的计算节点数量
- **CPU 使用率**: 显示系统整体CPU使用情况，包含警戒状态
- **内存使用率**: 显示系统内存使用情况

### 2. 图表区域
- **任务执行情况**: 折线图显示一周内任务的成功、失败、等待中状态趋势
- **节点状态分布**: 饼图显示所有节点的状态分布（运行中、离线、维护中、异常）

### 3. 最近任务表格
显示最近的任务执行情况，包含：
- 任务ID
- 任务名称
- 执行节点
- 状态（运行中、等待中、失败、已完成）
- 开始时间
- 操作按钮（查看、停止、取消、重试）

## 依赖安装

为了显示图表，需要安装以下依赖：

```bash
npm install echarts echarts-for-react
```

或者使用 yarn：

```bash
yarn add echarts echarts-for-react
```

## 使用说明

1. 安装依赖后，将 `src/views/home/<USER>
   ```typescript
   import ReactECharts from 'echarts-for-react';
   ```

2. 将 `ChartPlaceholder` 组件替换为 `ReactECharts` 组件：
   ```typescript
   // 替换前
   <ChartPlaceholder option={taskExecutionOption} style={{ height: '400px' }} />
   
   // 替换后
   <ReactECharts option={taskExecutionOption} style={{ height: '400px' }} />
   ```

## 测试数据

页面中包含了完整的测试数据：

- 统计数据包含4个关键指标
- 图表数据包含一周的任务执行趋势
- 表格数据包含3条最近任务记录

## 样式说明

页面使用了 CSS Modules 进行样式管理，样式文件位于 `index.module.css`，包含：
- 卡片悬停效果
- 响应式布局
- 图表占位符样式

## 响应式设计

页面支持响应式设计：
- 统计卡片在不同屏幕尺寸下自适应布局
- 图表区域在小屏幕下垂直排列
- 表格支持横向滚动

## 后续扩展

可以根据实际需求扩展以下功能：
- 实时数据更新
- 更多图表类型
- 数据筛选和搜索
- 导出功能
- 权限控制
