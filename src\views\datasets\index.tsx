import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Input, Select, Typography } from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import AddDatasetDrawer from './components/add-dataset-drawer';

interface DatasetData {
  id: string;
  name: string;
  source: string;
  type: string;
  permission: string;
  format: string;
  totalTokens: string;
  description: string;
  createTime: string;
}

const mockData: DatasetData[] = [
  {
    id: '1',
    name: 'alpaca_zh_demo',
    source: '用户上传',
    type: '训练集/测评集',
    permission: '训练，优化，测评',
    format: '纯文本',
    totalTokens: '12k',
    description: '中文数据集',
    createTime: '2025-02-20 13:40:12',
  },
  {
    id: '2',
    name: 'alpaca_en_demo',
    source: '内置',
    type: '训练集',
    permission: '推理，训练，优化，测评',
    format: 'prompt+response',
    totalTokens: '215.23K',
    description: '英文数据集',
    createTime: '2025-02-20  13:53:26',
  },
];

const DatasetList: React.FC = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [selectedSource, setSelectedSource] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<DatasetData[]>([]);

  useEffect(() => {
    // 模拟加载数据
    setLoading(true);
    setTimeout(() => {
      setData(mockData);
      setLoading(false);
    }, 300);
  }, []);

  // Filter options
  const sourceOptions = [
    { value: 'all', label: '全部' },
    { value: 'built_in', label: '内置' },
    { value: 'user_upload', label: '用户上传' },
  ];

  const typeOptions = [
    { value: 'all', label: '全部' },
    { value: 'training', label: '训练集' },
    { value: 'evaluation', label: '测评集' },
  ];

  const filteredData = data.filter((item) => {
    const nameMatch = item.name.toLowerCase().includes(searchText.toLowerCase());
    const sourceMatch =
      selectedSource === 'all' ||
      item.source === sourceOptions.find((opt) => opt.value === selectedSource)?.label;
    const typeMatch =
      selectedType === 'all' ||
      item.type.includes(
        typeOptions.find((opt) => opt.value === selectedType)?.label || '',
      );

    return nameMatch && sourceMatch && typeMatch;
  });

  const columns = [
    {
      title: '数据集名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (_: string, record: DatasetData) => (
        <div>
          <Typography.Link onClick={() => handleViewDetail(record.id)}>
            {record.name}
          </Typography.Link>
        </div>
      ),
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: '权限',
      dataIndex: 'permission',
      key: 'permission',
      width: 200,
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      width: 100,
    },
    {
      title: '总token数',
      dataIndex: 'totalTokens',
      key: 'totalTokens',
      width: 100,
    },
    {
      title: '数据集描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
    },
    {
      title: '操作',
      key: 'operation',
      width: 80,
      render: () => (
        <Space size="middle">
          <a onClick={() => {}} className="text-blue-600 hover:text-blue-800">
            删除
          </a>
        </Space>
      ),
    },
  ];

  const handleAddDataset = () => {
    setDrawerVisible(true);
  };

  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  const handleDrawerFinish = (values: any) => {
    console.log('Form values:', values);
    setDrawerVisible(false);
  };

  const handleViewDetail = (id: string) => {
    navigate(`/datasets/detail/${id}`);
  };

  return (
    <div className="p-6 bg-white">
      <div className="flex items-center mb-4 gap-2">
        <Input
          placeholder="请输入"
          onChange={(e) => setSearchText(e.target.value)}
          className="w-64"
          allowClear
          prefix={<SearchOutlined />}
        />
        <Select
          defaultValue="all"
          style={{ width: 160 }}
          options={sourceOptions}
          placeholder="来源"
          onChange={(value) => setSelectedSource(value)}
        />
        <Select
          defaultValue="all"
          style={{ width: 160 }}
          options={typeOptions}
          placeholder="类型"
          onChange={(value) => setSelectedType(value)}
        />
        <Button
          type="primary"
          className="ml-auto bg-blue-500"
          icon={<PlusOutlined />}
          onClick={handleAddDataset}
        >
          添加数据集
        </Button>
      </div>
      <Table
        loading={loading}
        columns={columns}
        dataSource={filteredData}
        rowKey="name"
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
      <AddDatasetDrawer
        open={drawerVisible}
        onClose={handleDrawerClose}
        onFinish={handleDrawerFinish}
      />
    </div>
  );
};

export default DatasetList;
